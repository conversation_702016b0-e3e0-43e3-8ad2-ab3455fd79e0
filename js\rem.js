(function () {
    // 设计稿基准尺寸（一般为设计稿宽度）
    const designWidth = 750;
    // 期望的1rem与px的比例
    const fontSizeBase = 100;

    function setRootFontSize () {
        const docEl = document.documentElement;
        // 获取当前设备宽度
        const clientWidth = docEl.clientWidth || window.innerWidth;
        // 如果屏幕宽度大于设计稿宽度，则按设计稿宽度计算
        const width = Math.min(clientWidth, designWidth);

        // 计算当前根字体大小（实现1rem=100px的核心逻辑）
        const fontSize = (width / designWidth) * fontSizeBase;

        // 设置根元素字体大小
        docEl.style.fontSize = fontSize + 'px';
    }

    // 初始化执行
    setRootFontSize();
    // 添加事件监听
    window.addEventListener('resize', setRootFontSize);
    window.addEventListener('pageshow', function (e) {
        if (e.persisted) {
            setRootFontSize();
        }
    });
})();