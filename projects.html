<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LNT</title>
    <script src="./js/vue.js"></script>
    <script src="./js/jquery-1.11.3.min.js"></script>
    <script src="./js/swiper.animate1.0.3.min.js"></script>
    <script src="./js/element.js"></script>
    <script src="./js/rem.js"></script>
    <script src="./js/swiper-bundle.min.js"></script>
    <link rel="stylesheet" href="./css/element.css">
    <link rel="stylesheet" href="./css/index.css">
    <link rel="stylesheet" href="./css/long.css">
    <link rel="stylesheet" href="./css/animate.min.css">
    <link rel="stylesheet" href="./css/swiper.min.css">
</head>

<body>
    <div class="headers" id="header">
        <div class="flex justify-between header_contain">
            <img src="./images/logo.png" alt="">
            <div class="flex header-right" :class="'active-' + curentIndex">
                <div :class="{ 'active':curentIndex == k }" class="flex items-center justify-center items"
                    v-for="(v,k) in barList" :key="k" @click="changeIndex(k)">{{ v.name }}
                </div>
            </div>
        </div>
    </div>
    <div class="projectspage" id="projects">
        <img class="headimg" src="./images/sl1.png" alt="">
        <div class="jh">
            <img class="jhimg" src="./images/sl2.png" alt="">
            <div class="jhmessage">
                <div class="title">什么是“无痕山野聚焦”计划?</div>
                <div class="maintxt">无痕山野聚焦”计划将我们致力的决心紧密集与致力于维护其健康与美丽的决心紧密集合。聚焦计划旨</div>
            </div>
        </div>
        <div class="hzhb">
               <div class="titleh">
                    “无痕山野聚集”计划合作伙伴
               </div>
               <img class="hx" src="./images/heng.png" alt="">
               <div class="pplist">
                    <div class="ppone" v-for="item in ppitems" :key="item">
                        <img class="ppimg" :src="item.img" alt="">
                        <div class="name">{{item.name}}</div>
                    </div>
               </div>
        </div>
        <div class="bzrz">
            <div class="bztitle">黄金标准认证</div>
            <div class="bztxt">黄金标准是“无痕山野”授予的最高级别认证。获得此项目认证的场所、组织及项目，因其在“无痕山野”原则时间中国的卓越表现，将在国内</div>
            <div class="jdlist">
                <div class="jdone" v-for="item in jditems" :key="item">
                    <div class="jdtitle">{{item.title}}</div>
                    <div class="jdtxt">{{item.message}}</div>
                    <div class="type">{{item.type}}</div>
                    <img class="jdicon" src="./images/jdicon.png" alt="">
                </div>
            </div>
            <img class="xmicon" src="./images/xmicon.png" alt="">
        </div>
        <div class="rcsh">
            <img class="rcshimg" src="./images/rcshimg.png" alt="">
            <div class="rcshpart">
                <div class="shtitle">
                    日常生活中“无痕山野”
                </div>
                <div class="shtxt">
                    “无痕山野”不仅是户外原则，更是塑造日常决策的可持续生活哲学。依托实证研究与实用技能，我们赋能个体在家庭与社区中践行可持续抉择。从减少浪费和节约用水到做出明智的消费者选择，“无痕山野”可融入我们的日常生活。有助于守护山野也适用于我们的社区公共空间及办公环境的持续改造。

通过在日常生活中践行“无痕山野”，我们可以做出微小而有意义的改变，这些改变共同产生巨大影响，确保为子孙后代创造一个更健康的星球。
                </div>
            </div>
        </div>
    </div>
    <script>
        new Vue({
            el: '#header',
            data: {
                curentIndex: 0,
                barList: [
                    { name: '首页', path: 'index.html' },
                    { name: '国际项目', path: 'about.html' },
                    { name: '合作机构', path: 'product.html' },
                    { name: '联系我们', path: 'product.html' },
                    { name: '资源下载', path: 'product.html' },
                ]
            },
            methods: {
                changeIndex (e) {
                    this.curentIndex = e;
                }
            },
        })
        new Vue({
            el: '#projects',
            data: {
                ppitems: [
                    {
                        id: 1,
                        img: '/images/sl3.png',
                        name: 'XXX品牌'
                    },
                    {
                        id: 1,
                        img: '/images/sl3.png',
                        name: 'XXX品牌'
                    }
                ],
                jditems: [
                    {
                        id: 1,
                        title: '第一阶段',
                        message: '认证授予基于“无痕山野”评估体系。最有效的初步举措是运用该评估工具明确现状基准水平，并制定后续阶段的行动计划',
                        type: '评估'
                    },
                    {
                        id: 1,
                        title: '第二阶段',
                        message: '认证授予基于“无痕山野”评估体系。最有效的初步举措是运用该评估工具明确现状基准水平，并制定后续阶段的行动计划',
                        type: '培训'
                    },
                    {
                        id: 1,
                        title: '第三阶段',
                        message: '认证授予基于“无痕山野”评估体系。最有效的初步举措是运用该评估工具明确现状基准水平，并制定后续阶段的行动计划',
                        type: '实施'
                    }
                ]
            },
            methods: {
            },
        })


    </script>
</body>

</html>