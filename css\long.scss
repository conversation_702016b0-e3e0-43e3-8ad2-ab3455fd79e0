.projectspage {
  width: 100%;
  background-color: #ffffff;
  .headimg {
    width: 100%;
    height: 886px;
  }
  .jh {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px;
    .jhimg {
      width: 206px;
      height: 238px;
    }
    .jhmessage {
      max-width: 50%;
      margin-left: 33px;
      display: grid;
      align-items: center;
      justify-items: center;
      .title {
        font-family: Source Han Sans CN;
        font-weight: bold;
        font-size: 24px;
        color: #0e2942;
        line-height: 40px;
      }
      .maintxt {
        font-family: Source Han Sans CN;
        font-size: 20px;
        color: #0e2942;
        line-height: 40px;
      }
    }
  }
  .hzhb {
    width: 100%;
    padding: 59px 0 80px 0;
    background: #f6f6f6;
    box-sizing: border-box;
    display: grid;
    align-items: center;
    justify-items: center;
    .titleh {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 38px;
      color: #00a7e9;
    }
    .hx {
      width: 650px;
      margin-top: 20px;
    }
    .pplist {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 130px;
      margin-top: 50px;
      .ppone {
        display: grid;
        align-items: center;
        justify-items: center;
        .ppimg {
          width: 120px;
          height: 120px;
          border-radius: 50%;
        }
        .name {
          margin-top: 23px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #00a7e9;
        }
      }
    }
  }
  .bzrz {
    padding: 100px 0;
    display: grid;
    align-items: center;
    justify-items: center;
    .bztitle {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 38px;
      color: #0e2942;
      line-height: 67px;
    }
    .bztxt {
      margin-top: 39px;
      max-width: 1115px;
      font-family: Source Han Sans CN;
      font-weight: 300;
      font-size: 18px;
      color: #666666;
      line-height: 27px;
    }
    .jdlist {
      margin-top: 61px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 37px;
      .jdone {
        display: grid;
        align-items: center;
        justify-items: center;
        max-width: 204px;
        .jdtitle {
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #0e2942;
          line-height: 38px;
        }
        .jdtxt {
          margin-top: 22px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 16px;
          color: #969fa9;
          line-height: 24px;
        }
        .type {
          font-family: Source Han Sans CN;
          font-weight: bold;
          font-size: 30px;
          color: #00a7e9;
          line-height: 38px;
          position: relative;
          top: 26px;
        }
        .jdicon {
          width: 100%;
          height: 161px;
        }
      }
    }
    .xmicon {
      margin-top: 13px;
      width: 338px;
      height: 182px;
    }
  }
  .rcsh {
    width: 100%;
    padding: 80px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 45px;
    .rcshimg {
      width: 551px;
      height: 338px;
    }
    .rcshpart {
      display: grid;
      align-items: center;
      justify-content: flex-start;
      max-width: 25%;

      .shtitle {
        font-family: Source Han Sans CN;
        font-weight: 800;
        font-size: 38px;
        color: #0e2942;
        line-height: 36px;
      }
      .shtxt {
        margin-top: 39px;
        font-family: Source Han Sans CN;
        font-weight: 200;
        font-size: 20px;
        color: #000000;
        line-height: 36px;
      }
    }
  }
}

.partnerspage {
  width: 100%;
  background-color: #ffffff;
  .headimg {
    width: 100%;
    height: 734px;
  }
  .headpart {
    display: grid;
    align-items: center;
    justify-items: center;
    margin-top: -550px;
    .txt1 {
      font-family: Source Han Sans CN;
      font-weight: 800;
      font-size: 60px;
      color: #ffffff;
      line-height: 100px;
    }
    .card {
      margin-top: 60px;
      width: 80%;
      height: 515px;
      background-image: url(../images/cardimg.png);
      background-size: 100% 100%;
      display: grid;
      align-content: center;
      justify-items: center;
      .txt2 {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 20px;
        color: #0e2942;
        line-height: 48px;
      }
    }
  }
  .xstx {
    margin-top: 48px;
    width: 100%;
    padding: 60px 0 70px 0;
    background: #f6f6f6;
    display: grid;
    align-items: center;
    justify-items: center;
    .titlexs {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 38px;
      color: #00a7e9;
    }
    .hengx {
      width: 650px;
      height: 2px;
      margin-bottom: 62px;
    }
    .txt4 {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 20px;
      color: #00a7e9;
      line-height: 36px;
    }
  }
  .addzyz {
    width: 100%;
    padding: 80px 20% 123px 20%;
    background-image: url(../images/zyzimg.png);
    background-size: 100% 100%;
    display: grid;
    align-items: center;
    justify-items: center;
    .titlezyz {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 38px;
      color: #ffffff;
      line-height: 48px;
    }
    .txt5 {
      margin-top: 59px;
      font-family: Source Han Sans CN;
      font-weight: 300;
      font-size: 20px;
      color: #ffffff;
      line-height: 48px;
    }
  }
  .bottompart {
    width: 100%;
    padding: 106px 0 158px 0;
    background: #f6f6f6;
    display: grid;
    align-items: center;
    justify-items: center;
    .titlehzjg {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 38px;
      color: #404450;
      line-height: 128px;
    }
    .txt6 {
      width: 100%;
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 24px;
      color: #666666;
      line-height: 30px;
      display: flex;
      align-items: center;
    }
    .txt6::before,
    .txt6::after {
      content: "";
      flex: 1;
      height: 1px;
      background: #ccc;
      margin: 0 10px;
    }
    .hzhblist {
      margin-top: 129px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 95px;
      .hzhbone {
        display: grid;
        align-items: center;
        justify-items: center;
        max-width: 148px;
        .hzhbimg {
          width: 148px;
          height: 170px;
        }
        .titlehz {
          font-family: Source Han Sans CN;
          font-weight: 500;
          font-size: 24px;
          color: #333333;
          line-height: 30px;
          margin-top: 34px;
        }
        .txt7 {
          margin-top: 22px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          font-size: 18px;
          color: #666666;
          line-height: 30px;
        }
      }
    }
  }
}
.contactus {
  width: 100%;
  background-color: #ffffff;
  .headpart {
    width: 100%;
    height: 750px;
    background-image: url(../images/contactusimg.png);
    background-size: 100% 100%;
    display: grid;
    align-content: center;
    justify-items: flex-start;
    .txt9 {
      margin-top: 30px;
      margin-left: 30%;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 36px;
      color: #ffffff;
      line-height: 100px;
    }
  }
  .mainpart {
    display: grid;
    align-items: center;
    justify-items: center;
    padding: 101px 0 100px 0;
    .titlelx {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 38px;
      color: #00a7e9;
    }
    .hx {
      margin-top: 20px;
      width: 650px;
      height: 2px;
    }
    .lxlist {
      margin-top: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 113px;
      .lxone {
        width: 195px;
        height: 325px;
        display: grid;
        justify-items: center;
        align-items: center;
        background-image: url(../images/ewmbg.png);
        background-size: 100% 100%;
        .ewm {
            margin-top: 60px;
          width: 118px;
          height: 118px;
        }
      }
    }
  }
}
