<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LNT</title>
    <script src="./js/vue.js"></script>
    <script src="./js/jquery-1.11.3.min.js"></script>
    <script src="./js/swiper.animate1.0.3.min.js"></script>
    <script src="./js/element.js"></script>
    <script src="./js/rem.js"></script>
    <script src="./js/swiper-bundle.min.js"></script>
    <link rel="stylesheet" href="./css/element.css">
    <link rel="stylesheet" href="./css/index.css">
    <link rel="stylesheet" href="./css/long.css">
    <link rel="stylesheet" href="./css/animate.min.css">
    <link rel="stylesheet" href="./css/swiper.min.css">
</head>

<body>
    <div class="headers" id="header">
        <div class="flex justify-between header_contain">
            <img src="./images/logo.png" alt="">
            <div class="flex header-right" :class="'active-' + curentIndex">
                <div :class="{ 'active':curentIndex == k }" class="flex items-center justify-center items"
                    v-for="(v,k) in barList" :key="k" @click="changeIndex(k)">{{ v.name }}
                </div>
            </div>
        </div>
    </div>
    <div class="contactus" id="contactus">
        <div class="headpart">
            <div class="txt9">地址:四川省成都市武侯区金履二路富顿中心F座 2楼</div>
            <div class="txt9">电话:XX</div>
            <div class="txt9"> 邮箱:<EMAIL></div>
        </div>
        <div class="mainpart">
            <div class="titlelx">联系我们</div>
            <img class="hx" src="./images/heng.png" alt="">
            <div class="lxlist">
                <div class="lxone" v-for="item in lxitems" :key="item">
                    <img class="ewm" :src="item.ewm" alt="">
                </div>
            </div>
        </div>
    </div>
    <script>
        new Vue({
            el: '#header',
            data: {
                curentIndex: 0,
                barList: [
                    { name: '首页', path: 'index.html' },
                    { name: '国际项目', path: 'about.html' },
                    { name: '合作机构', path: 'product.html' },
                    { name: '联系我们', path: 'product.html' },
                    { name: '资源下载', path: 'product.html' },
                ]
            },
            methods: {
                changeIndex (e) {
                    this.curentIndex = e;
                }
            },
        })
        new Vue({
            el: '#contactus',
            data: {
                lxitems: [
                    {
                        id: 1,
                        ewm: '/images/ewm.jpg',
                    },
                    {
                        id: 2,
                        ewm: '/images/ewm.jpg',
                    },
                    {
                        id: 3,
                        ewm: '/images/ewm.jpg',
                    }
                ]
            },
            methods: {
            },
        })


    </script>
</body>

</html>