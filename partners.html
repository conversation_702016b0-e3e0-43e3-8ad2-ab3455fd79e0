<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LNT</title>
    <script src="./js/vue.js"></script>
    <script src="./js/jquery-1.11.3.min.js"></script>
    <script src="./js/swiper.animate1.0.3.min.js"></script>
    <script src="./js/element.js"></script>
    <script src="./js/rem.js"></script>
    <script src="./js/swiper-bundle.min.js"></script>
    <link rel="stylesheet" href="./css/element.css">
    <link rel="stylesheet" href="./css/index.css">
    <link rel="stylesheet" href="./css/long.css">
    <link rel="stylesheet" href="./css/animate.min.css">
    <link rel="stylesheet" href="./css/swiper.min.css">
</head>

<body>
    <div class="headers" id="header">
        <div class="flex justify-between header_contain">
            <img src="./images/logo.png" alt="">
            <div class="flex header-right" :class="'active-' + curentIndex">
                <div :class="{ 'active':curentIndex == k }" class="flex items-center justify-center items"
                    v-for="(v,k) in barList" :key="k" @click="changeIndex(k)">{{ v.name }}
                </div>
            </div>
        </div>
    </div>
    <div class="partnerspage" id="partners">
        <img class="headimg" src="./images/sl4.png" alt="">
        <div class="headpart">
            <div class="txt1">加入我们</div>
            <div class="txt1">加入全球千万无痕守护者</div>
            <div class="card">
                <div class="txt2">您的支持将直接助力:生态保护行动一保护环境从我们的每一次出行开始;</div>
                <div class="txt2">教育赋能计划-向身边更多的人传播”无痕山野“理念和技能;</div>
                <div class="txt2">可持续未来共建--推动低碳芦外实践，降低入力活动对环境的影响。</div>
            </div>
        </div>
        <div class="xstx">
            <div class="titlexs">携手同行，我们能够</div>
            <img class="hengx" src="./images/heng.png" alt="">
            <div class="txt4">让每一片山林永葆纯净</div>
            <div class="txt4">让每一次户外活动都成为对自然的礼赞</div>
            <div class="txt4">让子孙后代仍能享有这颗星球的壮丽与生机</div>
        </div>
        <div class="addzyz">
            <div class="titlezyz">加入志愿者</div>
            <div class="txt5">通过传播“无痕山野”技能和环保伦理，志愿者在帮助保护户外活动方面发挥着至关重要的作用。通过培训、外联活动和服务项目推动“无痕山野”计划，我们一直在寻找有热情的人来帮助支持“无痕山野”推广工作，成为志愿者有机会参与到未来“无痕山野”发起的系列活动。</div>
        </div>
        <div class="bottompart">
            <div class="titlehzjg">合作机构</div>
            <div class="txt6">副标题副标题副标题副标题副标题</div>
            <div class="hzhblist">
                <div class="hzhbone" v-for="item in hzhbitems" :key="item">
                    <img class="hzhbimg" src="./images/hzhb.png" alt="">
                    <div class="titlehz">{{item.title}}</div>
                    <div class="txt7">{{item.js}}</div>
                </div>
            </div>
        </div>
    </div>
    <script>
        new Vue({
            el: '#header',
            data: {
                curentIndex: 0,
                barList: [
                    { name: '首页', path: 'index.html' },
                    { name: '国际项目', path: 'about.html' },
                    { name: '合作机构', path: 'product.html' },
                    { name: '联系我们', path: 'product.html' },
                    { name: '资源下载', path: 'product.html' },
                ]
            },
            methods: {
                changeIndex (e) {
                    this.curentIndex = e;
                }
            },
        })
        new Vue({
            el: '#partners',
            data: {
                hzhbitems: [
                    {
                        id: 1,
                        title: '合作伙伴',
                        js: '介绍介绍介绍 介绍介绍介绍'
                    },
                    {
                        id: 1,
                        title: '合作伙伴',
                        js: '介绍介绍介绍 介绍介绍介绍'
                    },
                    {
                        id: 1,
                        title: '合作伙伴',
                        js: '介绍介绍介绍 介绍介绍介绍'
                    }
                ]
            },
            methods: {
            },
        })


    </script>
</body>

</html>