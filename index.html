<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LNT</title>
    <script src="./js/vue.js"></script>
    <script src="./js/jquery-1.11.3.min.js"></script>
    <script src="./js/swiper.animate1.0.3.min.js"></script>
    <script src="./js/element.js"></script>
    <script src="./js/rem.js"></script>
    <script src="./js/swiper-bundle.min.js"></script>
    <link rel="stylesheet" href="./css/swiper.min.css">
    <link rel="stylesheet" href="./css/element.css">
    <link rel="stylesheet" href="./css/animate.min.css">
    <link rel="stylesheet" href="./css/long.css">
    <link rel="stylesheet" href="./css/index.css">
</head>

<body>
    <div class="headers" id="header">
        <div class="flex justify-between header_contain">
            <img src="./images/logo.png" alt="">
            <div class="flex header-right" :class="'active-' + curentIndex">
                <div :class="{ 'active':curentIndex == k }" class="flex items-center justify-center items"
                    v-for="(v,k) in barList" :key="k" @click="changeIndex(k)">{{ v.name }}
                </div>
            </div>
        </div>
    </div>
    <div class="index" id="index">
        <!-- 轮播图区域 -->
        <div class="banner-section">
            <div class="swiper banner-swiper">
                <div class="swiper-wrapper">
                    <div class="swiper-slide" v-for="(item, index) in bannerList" :key="index">
                        <img :src="item.image" alt="">
                    </div>
                </div>
                <div class="swiper-pagination"></div>
                <div class="swiper-button-prev">
                    <i class="el-icon-caret-left"></i>
                </div>
                <div class="swiper-button-next">
                    <i class="el-icon-caret-right"></i>
                </div>
            </div>
        </div>

        <!-- 蓝色介绍区域 -->
        <div class="intro-section">
            <div class="container">
                <div class="intro-content">
                    <h2>{{ introData.title }}</h2>
                    <p>{{ introData.description }}</p>
                </div>
            </div>
        </div>

        <!-- 时间轴区域 -->
        <div class="timeline-section">
            <div class="container">
                <div class="timeline-container">
                    <div class="timeline-item" v-for="(item, index) in timelineData" :key="index"
                        :class="{ 'timeline-item-right': index % 2 === 1 }">
                        <div class="timeline-year">{{ item.year }}</div>
                        <div class="timeline-content">
                            <div class="timeline-text">
                                <div class="timeline-year-display">{{ item.year }}</div>
                                <div class="timeline-description">{{ item.text }}</div>
                                <div class="timeline-image" v-if="item.image">
                                    <img :src="item.image" :alt="item.year + '年事件图片'">
                                </div>
                            </div>
                        </div>
                        <div class="timeline-badge">{{ item.badge }}</div>
                        <div class="timeline-dot"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 世界地图区域 -->
        <div class="map-section">
            <div class="world-map">
                <img src="./images/world-map.png" alt="世界地图">
            </div>
        </div>

        <!-- 合作伙伴区域 -->
        <div class="partners-section">
            <div class="container">
                <div class="partners-grid">
                    <div class="partner-item" v-for="(partner, index) in partnersData.partners" :key="index">
                        <div class="partner-logo">
                            <img :src="partner.logo" :alt="partner.name">
                        </div>
                        <div class="partner-name">{{ partner.name }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 介绍模块 -->
        <div class="intro-module">
            <div class="container">
                <div class="intro-content">
                    <h2>{{ introData.title }}</h2>
                    <p>{{ introData.description }}</p>
                    <div class="intro-tags">
                        <span class="intro-tag" v-for="(tag, index) in introData.tags" :key="index">
                            {{ tag }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 富文本模块 -->
        <div class="richtext-module">
            <div class="container">
                <div class="richtext-content" v-html="richtextData.content">
                </div>
            </div>
        </div>
    </div>

    <!-- Footer 底部 -->
    <footer class="footer" id="footer">
        <div class="footer-container">
            <!-- 深蓝色主要内容区域 -->
            <div class="footer-main">
                <div class="footer-content">
                    <!-- Logo区域 -->
                    <div class="footer-logo">
                        <img src="./images/logo.png" alt="Leave No Trace China">
                    </div>

                    <!-- 文字内容区域 -->
                    <div class="footer-text">
                        <p>{{ footerData.description }}</p>
                        <p>{{ footerData.mission }}</p>
                    </div>
                </div>
            </div>

            <!-- 浅蓝色版权信息区域 -->
            <div class="footer-copyright">
                <div class="copyright-content">
                    <span>{{ footerData.copyright }}</span>
                    <span>{{ footerData.icp }}</span>
                    <span>{{ footerData.publicSecurity }}</span>
                </div>
            </div>
        </div>
    </footer>


    <script>
        new Vue({
            el: '#header',
            data: {
                curentIndex: 0,
                barList: [
                    { name: '首页', path: 'index.html' },
                    { name: '国际项目', path: 'about.html' },
                    { name: '合作机构', path: 'product.html' },
                    { name: '联系我们', path: 'product.html' },
                    { name: '资源下载', path: 'product.html' },
                ]
            },
            methods: {
                changeIndex (e) {
                    this.curentIndex = e;
                }
            },
        })
        new Vue({
            el: '#index',
            data: {
                bannerList: [
                    { image: './images/contactusimg.png' },
                    { image: './images/contactusimg.png' },
                    { image: './images/contactusimg.png' }
                ],
                introData: {
                    title: 'Leave No Trace 无痕山林',
                    description: 'Leave No Trace（无痕山林）是一套户外环保行为准则，旨在减少人类活动对自然环境的影响。通过教育和实践，我们致力于培养负责任的户外行为，保护珍贵的自然资源，让后代也能享受到美丽的自然环境。',
                    features: [
                        {
                            icon: 'el-icon-location',
                            title: '事前充分规划与准备',
                            description: '了解相关法规和注意事项，做好充分的行前准备'
                        },
                        {
                            icon: 'el-icon-guide',
                            title: '在可承受地点行走宿营',
                            description: '选择合适的路线和营地，减少对环境的破坏'
                        },
                        {
                            icon: 'el-icon-delete',
                            title: '适当处理垃圾维护环境',
                            description: '带走所有垃圾，保持环境的原始状态'
                        }
                    ]
                },
                timelineData: [
                    {
                        year: '20XX',
                        badge: '20XX年大事',
                        text: 'Leave No Trace理念在20XX年第一次引入中国',
                        image: './images/contactusimg.png'
                    },
                    {
                        year: '20XX',
                        badge: '20XX年大事',
                        text: '在中国举办了第一届Leave NoTrace二级讲师课程，并开始在中国推广培训师，截止目前，中国共计在册一级讲师约XXX人，二级讲师约XXX人'
                    },
                    {
                        year: '2025',
                        badge: '20XX年大事',
                        text: '中营利组织Leave No Trace China成立，作为LNT官方合作伙伴分析构架中国开展相关Leave No Trace相关主题活动',
                        image: './images/contactusimg.png'
                    }
                ],
                mapData: {
                    title: 'LEAVE NO TRACE 全球推广',
                    markers: [
                        { name: '北美', left: '20%', top: '30%' },
                        { name: '欧洲', left: '50%', top: '25%' },
                        { name: '亚洲', left: '70%', top: '35%' },
                        { name: '澳洲', left: '80%', top: '70%' },
                        { name: '南美', left: '30%', top: '65%' }
                    ]
                },
                partnersData: {
                    title: '合作伙伴',
                    partners: [
                        { name: '无痕国际组织', logo: './images/contactusimg.png' },
                        { name: '无痕山野加拿大', logo: './images/contactusimg.png' },
                        { name: '无痕山野爱尔兰', logo: './images/contactusimg.png' },
                        { name: '无痕山野日本', logo: './images/contactusimg.png' },
                        { name: '无痕山野韩国', logo: './images/contactusimg.png' },
                        { name: '无痕山野新西兰', logo: './images/contactusimg.png' }
                    ]
                },
                introData: {
                    title: '《不留痕迹的7项原则》为户外游客提供了一个易于学习的会议，帮助他们践行。',
                    description: '这些原则可以应用于任何户外地方，从前院的教育社区到遥远的荒野公园。更多在你的回忆中，每个假期都能成为一个特殊的主题，并享用了详细的内容，使您能够将大自然保护起来。这七项原则已经被数千万户外人员，但许多一直不知道。Leave NoTrace不断检查、评估和更新原则，并进行研究，以确保它们仍然是学习者、土地管理者和其他户外教育等的最新见解的体现。',
                    tags: [
                        '提前计划与准备',
                        '在可耐受地面行走和露营',
                        '妥善处理垃圾',
                        '保持自然原貌',
                        '野外安全用火',
                        '尊重野生动物',
                        '考虑其他户外活动者'
                    ]
                },
                richtextData: {
                    content: `
                        <div class="richtext-header">
                            <h2>无痕山野培训</h2>
                        </div>

                        <div class="richtext-intro">
                            <p>在我国推行"无痕山野"理念已经成为社会各界关注的焦点，我们"无痕山野"的讲师团队致力于传播这种教育理念，更重要的是代表我们的生活态度。"无痕山野"理念强调的是人与自然和谐共处的生活方式，"无痕山野"要求我们对自然环境保持敬畏之心，"无痕山野"培训的目的是自然环境的保护者，可持续发展的实践者。</p>

                            <p>"无痕山野"培训课程为力求深度挖掘各种教育内容形式，探索各种教育形式的最佳方式，结合各种教育形式进行培训。"无痕山野"教学的核心是人，要充分调动各种教育资源优势，作出正确的教育资源配置工人人的共同配合中的"无痕山野"的教育意识的形成的教学，无痕教育各种资源的综合运用，探索各种教育形式为力求达到各类教育课程，在中国的课堂教学改革为今后教育目标实现提供，在不断的实践中总结经验，改进教学方法，提高教学质量。</p>
                        </div>

                        <div class="richtext-cards">
                            <div class="richtext-card">
                                <div class="card-image">
                                    <img src="./images/contactusimg.png" alt="一级讲师培训">
                                </div>
                                <div class="card-content">
                                    <h3>一级讲师培训（2天）</h3>
                                    <p>学习课程的主要内容"无痕山野"理念（LNT）课程，内容包括无痕基础...</p>
                                    <p>认识无痕户外理念，了解无痕理念下的生活方式理念，认识无痕理念，</p>
                                    <p>无痕理念与户外理念的关系，无痕理念的内容和无痕理念的实践，无痕理念的</p>
                                    <p>教学理念应用，无痕基础课程的理念的自主学习"无痕山野"理念的，</p>
                                    <p>让学员各种无痕工作。</p>
                                </div>
                            </div>

                            <div class="richtext-card">
                                <div class="card-image">
                                    <img src="./images/contactusimg.png" alt="二级讲师培训">
                                </div>
                                <div class="card-content">
                                    <h3>二级讲师培训（3天）</h3>
                                    <p>二级户外课程的主要内容无痕户外理念的教育内容，无痕理念与全民户外理念</p>
                                    <p>教育理念，"无痕山野"理念与无痕教育理念，让无痕理念的户外理念无痕</p>
                                    <p>户外理念与自然环境理念，理念理念，基本无痕理念的理念，</p>
                                    <p>让户外理念应用自主无痕理念户外理念课程，无痕理念与无痕理念，理念理念与</p>
                                    <p>理念的"无痕山野"理念，理念二级无痕理念的户外理念的理念理念一级课</p>
                                    <p>程理念理念，理念无"无痕山野"理念无痕理念的理念的理念理念。</p>
                                </div>
                            </div>

                            <div class="richtext-card">
                                <div class="card-image">
                                    <img src="./images/contactusimg.png" alt="工作坊">
                                </div>
                                <div class="card-content">
                                    <h3>工作坊</h3>
                                    <p>通过各种方式进行各种无痕理念"无痕山野"理念学习户外理念课程理念的</p>
                                    <p>教学户外理念，理念无痕理念无痕理念工作坊，理念无"理念无痕理念户外理念</p>
                                    <p>，工作坊无痕理念无痕理念，理念无，无痕，无痕理念无痕理念无痕理念</p>
                                    <p>理念"无痕山野"无，无痕理念无，无痕理念的理念无痕理念无痕理念无痕</p>
                                    <p>理念无痕理念，理念无痕理念，理念无痕理念工作理念的理念理念。</p>
                                </div>
                            </div>

                            <div class="richtext-card">
                                <div class="card-image">
                                    <img src="./images/contactusimg.png" alt="技能课程">
                                </div>
                                <div class="card-content">
                                    <h3>技能课程</h3>
                                    <p>学习无痕理念课程理念的理念的理念，理念"无痕山野"理念"无"的理念无痕理念</p>
                                    <p>理念，理念无痕理念无痕理念，理念无痕理念无痕理念，理念无痕理念无痕理念</p>
                                    <p>理念理念，理念无痕理念的理念工作理念，"无痕山野"理念理念的理念的理念</p>
                                    <p>理念无痕理念"无痕山野"无痕理念的理念理念理念，理念无痕理念的理念的理念</p>
                                    <p>无痕理念理念无痕理念。</p>
                                </div>
                            </div>
                        </div>
                    `
                }
            },
            mounted () {
                this.initSwiper();
            },
            methods: {
                initSwiper () {
                    new Swiper('.banner-swiper', {
                        loop: true,
                        autoplay: {
                            delay: 3000,
                            disableOnInteraction: false,
                        },
                        navigation: {
                            nextEl: '.swiper-button-next',
                            prevEl: '.swiper-button-prev',
                        },
                        pagination: {
                            el: '.swiper-pagination',
                            clickable: true,
                        },
                    })
                }
            },
        })

        // Footer Vue实例
        new Vue({
            el: '#footer',
            data: {
                footerData: {
                    description: '我们的工作以科学为基础，"无痕山野"以科学和知识为先导，自主探索和发现一直是国际体系中不可或缺的重要组成部分。随着时间的推移',
                    mission: '这一理念不断深入人们的日常生活实践中的中国人行为方式教育人们户外运动和休闲娱乐大型活动体系，致力于我们人类引领社会的新时代的价值观和理念，营造适宜的为引导户外运动的生活方式。',
                    copyright: '©2015 LNT中国',
                    icp: '鲁A2-20044005号',
                    publicSecurity: '鲁公网安备44030702002388号'
                }
            }
        });

    </script>
</body>

</html>