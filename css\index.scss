:root {
    /* 颜色变量 */
    --primary: #3498db;
    --secondary: #2c3e50;
    --success: #2ecc71;
    --warning: #f39c12;
    --danger: #e74c3c;
    --light: #ecf0f1;
    --dark: #34495e;
    --gray: #95a5a6;

    /* 字体变量 */
    --font-base: .16rem;
    --font-family: 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;

    /* 间距变量 */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;

    /* 响应式断点 */
    --breakpoint-xs: 0;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;

    /* 阴影 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
}

*,
*::before,
*::after {
    font-size: var(--font-base);
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    line-height: 1.6;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    color: #333;
    overflow-x: hidden;
    min-height: 100vh;
    scroll-behavior: smooth;
}

/* ========== 元素初始化 ========== */
/* 段落 */
p {
    margin-bottom: var(--space-md);
    word-break: break-word;
    hyphens: auto;

    &:last-child {
        margin-bottom: 0;
    }
}

/* 标题 */
h1,
h2,
h3,
h4,
h5,
h6 {
    margin-top: 0;
    margin-bottom: var(--space-md);
    line-height: 1.2;
    font-weight: 600;
    color: var(--dark);
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.75rem;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.25rem;
}

h6 {
    font-size: 1rem;
}

/* 列表 */
ul,
ol {
    margin-bottom: var(--space-md);
    padding-left: var(--space-xl);

    ul,
    ol {
        margin-bottom: 0;
        margin-top: var(--space-xs);
    }
}

li {
    margin-bottom: var(--space-sm);
    position: relative;

    &:last-child {
        margin-bottom: 0;
    }
}

ul {
    list-style-type: disc;

    ul {
        list-style-type: circle;
    }
}

ol {
    list-style-type: decimal;
}

/* 链接 */
a {
    color: var(--primary);
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover,
    &:focus {
        color: var(--secondary);
        text-decoration: underline;
    }
}

/* 表格 */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--space-md);

    th,
    td {
        padding: var(--space-sm);
        border: 1px solid #dee2e6;
        text-align: left;
    }

    th {
        background-color: var(--light);
        font-weight: 600;
    }

    tr:nth-child(even) {
        background-color: rgba(0, 0, 0, 0.02);
    }
}

/* 图片 */
img {
    max-width: 100%;
    vertical-align: middle;
    border-style: none;
}

figure {
    margin: 0 0 var(--space-md) 0;

    img {
        display: block;
        margin: 0 auto;
    }

    figcaption {
        margin-top: var(--space-xs);
        font-size: 0.875rem;
        color: var(--gray);
        text-align: center;
    }
}

/* 表单元素 */
input,
button,
textarea,
select {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

button,
input {
    overflow: visible;
}

button,
select {
    text-transform: none;
}

label {
    display: inline-block;
    margin-bottom: var(--space-xs);
}

textarea {
    resize: vertical;
    min-height: 100px;
}

/* ========== FLEX布局系统 ========== */
/* 容器类 */
.flex {
    display: flex;
    flex-wrap: wrap;
}

.inline-flex {
    display: inline-flex;
}

/* 主轴方向 */
.flex-row {
    flex-direction: row;
}

.flex-row-reverse {
    flex-direction: row-reverse;
}

.flex-col {
    flex-direction: column;
}

.flex-col-reverse {
    flex-direction: column-reverse;
}

/* 主轴对齐 */
.justify-start {
    justify-content: flex-start;
}

.justify-end {
    justify-content: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}

/* 交叉轴对齐 */
.items-start {
    align-items: flex-start;
}

.items-end {
    align-items: flex-end;
}

.items-center {
    align-items: center;
}

.items-baseline {
    align-items: baseline;
}

.items-stretch {
    align-items: stretch;
}

/* 换行方式 */
.flex-wrap {
    flex-wrap: wrap;
}

.flex-nowrap {
    flex-wrap: nowrap;
}

.flex-wrap-reverse {
    flex-wrap: wrap-reverse;
}

/* 子元素 */
.flex-grow {
    flex-grow: 1;
}

.flex-shrink {
    flex-shrink: 1;
}

.flex-no-grow {
    flex-grow: 0;
}

.flex-no-shrink {
    flex-shrink: 0;
}

/* ========== 实用工具 ========== */
.full-width {
    width: 100%;
}

.full-height {
    height: 100%;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.shadow-sm {
    box-shadow: var(--shadow-sm);
}

.shadow-md {
    box-shadow: var(--shadow-md);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}

.rounded {
    border-radius: 0.25rem;
}

.rounded-circle {
    border-radius: 50%;
}

.cursor-pointer {
    cursor: pointer;
}

.bg-primary {
    background-color: var(--primary) !important;
    color: white;
}

.bg-secondary {
    background-color: var(--secondary) !important;
    color: white;
}

.headers {
    width: 100vw;
    background-color: #0e2942;
    height: 98px;

    .header_contain {
        width: 1200px;
        margin: 0 auto;
        height: 100%;

        img {
            object-fit: contain;
        }

        .header-right {
            position: relative;

            &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                width: 120px;
                height: 100%;
                background-color: #00a7e9;
                transform: translateX(0);
                transition: transform 0.3s ease-in-out;
                z-index: 1;
            }

            &::after {
                content: "";
                position: absolute;
                bottom: -.08rem;
                left: 0;
                transform: translateX(60px);
                width: 0;
                height: 0;
                border-left: 8px solid transparent;
                border-right: 8px solid transparent;
                border-top: 8px solid #00a7e9;
                transition: transform 0.3s ease-in-out;
                z-index: 3;
            }

            .items {
                position: relative;
                cursor: pointer;
                width: 120px;
                color: #fff;
                font-weight: 500;
                font-size: .18rem;
                z-index: 2;
                transition: all 0.3s ease;


            }

            // 根据不同的 active 索引位置移动背景和三角形
            &.active-0::before {
                transform: translateX(0);
            }

            &.active-0::after {
                transform: translateX(calc(-50% + 60px));
            }

            &.active-1::before {
                transform: translateX(120px);
            }

            &.active-1::after {
                transform: translateX(calc(-50% + 180px));
            }

            &.active-2::before {
                transform: translateX(240px);
            }

            &.active-2::after {
                transform: translateX(calc(-50% + 300px));
            }

            &.active-3::before {
                transform: translateX(360px);
            }

            &.active-3::after {
                transform: translateX(calc(-50% + 420px));
            }

            &.active-4::before {
                transform: translateX(480px);
            }

            &.active-4::after {
                transform: translateX(calc(-50% + 540px));
            }
        }
    }

    // 世界地图区域
    .map-section {
        padding: .8rem 0;

        .map-content {
            text-align: center;

            h2 {
                font-size: .32rem;
                font-weight: bold;
                color: #333;
                margin-bottom: .6rem;
            }

            .world-map {
                position: relative;
                display: inline-block;
                max-width: 100%;
                border: 2px dashed #00a7e9;
                border-radius: .1rem;
                padding: .3rem;
                background: white;

                img {
                    width: 100%;
                    height: auto;
                    display: block;
                }

                .map-marker {
                    position: absolute;
                    transform: translate(-50%, -50%);
                    cursor: pointer;

                    .marker-dot {
                        width: .12rem;
                        height: .12rem;
                        background: #00a7e9;
                        border-radius: 50%;
                        border: 2px solid white;
                        box-shadow: 0 2px 8px rgba(0, 167, 233, 0.4);
                        animation: pulse 2s infinite;
                    }

                    .marker-info {
                        position: absolute;
                        top: -.4rem;
                        left: 50%;
                        transform: translateX(-50%);
                        background: #333;
                        color: white;
                        padding: .04rem .08rem;
                        border-radius: .04rem;
                        font-size: .12rem;
                        white-space: nowrap;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    }

                    &:hover .marker-info {
                        opacity: 1;
                    }
                }

                @keyframes pulse {
                    0% {
                        box-shadow: 0 0 0 0 rgba(0, 167, 233, 0.7);
                    }

                    70% {
                        box-shadow: 0 0 0 .1rem rgba(0, 167, 233, 0);
                    }

                    100% {
                        box-shadow: 0 0 0 0 rgba(0, 167, 233, 0);
                    }
                }
            }
        }
    }

    // 合作伙伴区域
    .partners-section {
        background: white;
        padding: .8rem 0;

        h2 {
            text-align: center;
            font-size: .32rem;
            font-weight: bold;
            color: #333;
            margin-bottom: .6rem;
        }

        .partners-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: .3rem;
            max-width: 8rem;
            margin: 0 auto;

            .partner-item {
                border: 1px solid #e9ecef;
                border-radius: .08rem;
                padding: .2rem;
                text-align: center;
                transition: all 0.3s ease;
                aspect-ratio: 2/1;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover {
                    transform: translateY(-.02rem);
                    box-shadow: 0 .04rem .12rem rgba(0, 0, 0, 0.1);
                    border-color: #00a7e9;
                }

                img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                    filter: grayscale(100%);
                    transition: filter 0.3s ease;
                }

                &:hover img {
                    filter: grayscale(0%);
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        .container {
            width: 100%;
            padding: 0 .15rem;
        }

        .banner-section {
            height: 4rem;
        }

        .intro-section {
            padding: .6rem 0;

            .intro-content {
                h2 {
                    font-size: .24rem;
                }

                >p {
                    font-size: .14rem;
                }

                .intro-features {
                    flex-direction: column;
                    gap: .2rem;

                    .feature-item {
                        .feature-icon {
                            font-size: .3rem;
                        }

                        h3 {
                            font-size: .16rem;
                        }

                        p {
                            font-size: .12rem;
                        }
                    }
                }
            }
        }

        .map-section,
        .partners-section {
            padding: .6rem 0;

            h2 {
                font-size: .24rem;
            }
        }

        .partners-grid {
            grid-template-columns: repeat(2, 1fr);
            max-width: 6rem;
        }
    }
}


// Footer 底部样式
.footer {
    width: 100vw;
    margin-top: auto;

    .footer-container {
        width: 100%;

        // 深蓝色主要内容区域
        .footer-main {
            // background-color: #0e2942;
            background-image: url(/images/footback.png);
            background-size: cover;
            background-repeat: no-repeat;
            padding: .4rem 0;

            .footer-content {
                width: 12rem;
                margin: 0 auto;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 0 .2rem;

                .footer-logo {
                    flex-shrink: 0;

                    img {
                        height: .8rem;
                        width: auto;
                        object-fit: contain;
                    }
                }

                .footer-text {
                    flex: 1;
                    color: white;
                    padding: .6rem 0;

                    p {
                        font-size: .14rem;
                        line-height: 1.6;
                        margin: 0;
                        margin-bottom: .15rem;
                        opacity: 0.9;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }

        // 浅蓝色版权信息区域
        .footer-copyright {
            background-color: #00a7e9;
            padding: .15rem 0;

            .copyright-content {
                width: 12rem;
                margin: 0 auto;
                padding: 0 .2rem;
                display: flex;
                justify-content: center;
                align-items: center;
                gap: .3rem;
                flex-wrap: wrap;

                span {
                    color: white;
                    font-size: .12rem;
                    opacity: 0.9;
                    white-space: nowrap;
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        .footer-container {
            .footer-main {
                padding: .3rem 0;

                .footer-content {
                    width: 100%;
                    flex-direction: column;
                    text-align: center;
                    gap: .2rem;
                    padding: 0 .15rem;

                    .footer-logo {
                        img {
                            height: .6rem;
                        }
                    }

                    .footer-text {
                        p {
                            font-size: .12rem;
                            margin-bottom: .1rem;
                        }
                    }
                }
            }

            .footer-copyright {
                padding: .12rem 0;

                .copyright-content {
                    width: 100%;
                    flex-direction: column;
                    gap: .1rem;
                    padding: 0 .15rem;

                    span {
                        font-size: .11rem;
                    }
                }
            }
        }
    }
}

.index {
    width: 100vw;

    // 通用容器
    .container {
        width: 12rem;
        margin: 0 auto;
        padding: 0 .2rem;
    }

    // 轮播图区域
    .banner-section {
        position: relative;
        height: 6rem;
        overflow: hidden;

        .banner-swiper {
            position: relative;
            width: 100%;
            height: 100%;

            .swiper-slide {
                position: relative;
                background: #f0f0f0;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: .24rem;
                color: #333;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            // 分页器样式
            .swiper-pagination {
                bottom: .2rem;

                .swiper-pagination-bullet {
                    background: rgba(255, 255, 255, 0.5);
                    opacity: 1;
                    width: .12rem;
                    height: .12rem;

                    &.swiper-pagination-bullet-active {
                        background: #00a7e9;
                    }
                }
            }

            // 导航按钮样式 - 仿照图片设计
            .swiper-button-next,
            .swiper-button-prev {
                cursor: pointer;
                position: absolute !important;
                top: 50% !important;
                transform: translateY(-50%) !important;
                width: .5rem !important;
                height: .5rem !important;
                background: rgba(255, 255, 255, 0.8) !important;
                border-radius: 50% !important;
                color: #333 !important;
                z-index: 999 !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
                transition: all 0.3s ease !important;
                border: 1px solid rgba(255, 255, 255, 0.3) !important;
                backdrop-filter: blur(10px) !important;
                margin-top: 0 !important;
                opacity: 1 !important;
                visibility: visible !important;

                &::after {
                    display: none !important;
                }

                &::before {
                    display: none !important;
                }

                // Element UI 图标样式
                i {
                    font-size: .18rem !important;
                    color: #333 !important;
                    line-height: 1 !important;
                }

                &:hover {
                    background: rgba(255, 255, 255, 0.95) !important;
                    transform: translateY(-50%) scale(1.1) !important;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
                }

                &:active {
                    transform: translateY(-50%) scale(0.95) !important;
                }
            }

            .swiper-button-prev {
                left: .3rem !important;


            }

            .swiper-button-next {
                right: .3rem !important;


            }

            // 确保按钮在所有情况下都可见
            .swiper-button-disabled {
                opacity: 0.5 !important;
                visibility: visible !important;
            }
        }
    }

    // 蓝色介绍区域
    .intro-section {
        background-image: url(/images/back.png);
        background-size: cover;
        background-repeat: no-repeat;
        padding: .8rem 0;
        color: white;

        .intro-content {

            h2 {
                color: #fff;
                font-size: .32rem;
                font-weight: bold;
                margin-bottom: .3rem;
            }

            >p {
                font-size: .16rem;
                line-height: 1.8;
                opacity: 0.9;
            }


        }
    }

    // 时间轴区域
    .timeline-section {
        padding: .8rem 0;
        position: relative;

        .timeline-container {
            position: relative;
            margin: 0 auto;

            // 中央连接线
            &::before {
                content: '';
                position: absolute;
                left: 50%;
                top: 0;
                bottom: 0;
                width: 2px;
                background: #00a7e9;
                transform: translateX(-50%);
                z-index: 1;
            }

            .timeline-item {
                position: relative;
                display: flex;
                align-items: flex-start;
                margin-bottom: .8rem;
                padding: 0 .3rem;

                &:last-child {
                    margin-bottom: 0;
                }

                // 隐藏年份显示
                .timeline-year {
                    display: none;
                }

                .timeline-content {
                    flex: 1;
                    padding-left: .3rem;
                    max-width: 4.5rem;

                    .timeline-text {
                        font-size: .14rem;
                        line-height: 1.6;
                        color: #333;
                        background: white;
                        padding: .15rem;
                        border-radius: .08rem;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        position: relative;

                        // 左侧箭头 - 指向时间轴中心
                        &::before {
                            content: '';
                            position: absolute;
                            right: -.08rem;
                            top: .2rem;
                            width: 0;
                            height: 0;
                            border-top: .08rem solid transparent;
                            border-bottom: .08rem solid transparent;
                            border-left: .08rem solid white;
                        }

                        .timeline-year-display {
                            color: #00A7E9;
                            font-weight: bold;
                            font-size: .16rem;
                            margin-bottom: .08rem;
                            text-align: right;
                        }

                        .timeline-description {
                            margin-bottom: .1rem;
                        }

                        .timeline-image {
                            margin-top: .1rem;

                            img {
                                width: 100%;
                                height: 1.5rem;
                                object-fit: cover;
                                border-radius: .06rem;
                            }
                        }
                    }
                }

                // 蓝色标签替代圆点
                .timeline-badge {
                    position: absolute;
                    left: 50%;
                    top: .1rem;
                    transform: translateX(-50%);
                    background: #00a7e9;
                    color: white;
                    padding: .04rem .12rem;
                    border-radius: .12rem;
                    font-size: .12rem;
                    white-space: nowrap;
                    z-index: 2;
                    box-shadow: 0 2px 8px rgba(0, 167, 233, 0.3);
                }

                .timeline-dot {
                    display: none;
                }

                // 右侧布局
                &.timeline-item-right {
                    flex-direction: row-reverse;

                    .timeline-content {
                        padding-left: 0;
                        padding-right: .3rem;

                        .timeline-text {

                            // 右侧箭头 - 指向时间轴中心
                            &::before {
                                left: -.08rem;
                                right: auto;
                                border-left: none;
                                border-right: .08rem solid white;
                            }

                            .timeline-year-display {
                                text-align: left;
                            }
                        }
                    }
                }
            }
        }
    }

    // 世界地图区域
    .map-section {
        padding: .4rem 0;

        .world-map {
            width: 90vw;
            margin: 0 auto;
            position: relative;
            border-radius: .1rem;
            background: white;

            img {
                width: 100%;
                height: auto;
                display: block;
            }
        }
    }

    // 合作伙伴区域
    .partners-section {
        background: white;
        padding: .8rem 0;

        h2 {
            text-align: center;
            font-size: .32rem;
            font-weight: bold;
            color: #333;
            margin-bottom: .6rem;
        }

        .partners-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: .3rem;
            margin: 0 auto;

            .partner-item {
                cursor: pointer;
                text-align: center;
                transition: all 0.3s ease;
                display: flex;
                flex-direction: column;
                align-items: center;

                &:hover {
                    transform: translateY(-.02rem);
                }

                .partner-logo {
                    height: 2.7rem;
                    margin-bottom: .15rem;
                    border-radius: .12rem;
                    overflow: hidden;
                    box-shadow: 0 .02rem .08rem rgba(0, 0, 0, 0.1);
                    transition: all 0.3s ease;

                    &:hover {
                        box-shadow: 0 .04rem .16rem rgba(0, 0, 0, 0.15);
                    }

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        transition: transform 0.3s ease;
                    }

                    &:hover img {
                        transform: scale(1.05);
                    }
                }

                .partner-name {
                    font-size: .14rem;
                    color: #666;
                    font-weight: 500;
                    line-height: 1.4;
                    position: relative;
                    transition: color 0.3s ease;

                    // 下划线动画
                    &::after {
                        content: '';
                        position: absolute;
                        bottom: -.04rem;
                        left: 50%;
                        width: 0;
                        height: 2px;
                        background: #00a7e9;
                        transition: all 0.3s ease;
                        transform: translateX(-50%);
                    }
                }

                &:hover .partner-name {
                    color: #00a7e9;

                    &::after {
                        width: 100%;
                    }
                }
            }
        }
    }

    // 介绍模块
    .intro-module {
        background-image: url(/images/tback.png);
        background-size: cover;
        background-repeat: no-repeat;
        padding: .8rem 0;
        color: white;

        .intro-content {
            text-align: left;
            margin: 0 auto;

            h2 {
                font-size: .28rem;
                font-weight: bold;
                line-height: 1.5;
                margin-bottom: .2rem;
                color: white;
            }

            p {
                font-size: .16rem;
                line-height: 1.8;
                margin-bottom: .5rem;
                color: rgba(255, 255, 255, 0.9);
                text-align: justify;
            }

            .intro-tags {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: .15rem;

                .intro-tag {
                    background: rgba(255, 255, 255, 0.2);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: .25rem;
                    padding: .08rem .2rem;
                    font-size: .14rem;
                    color: white;
                    transition: all 0.3s ease;
                    cursor: pointer;

                    &:hover {
                        background: rgba(255, 255, 255, 0.3);
                        border-color: rgba(255, 255, 255, 0.5);
                        transform: translateY(-.02rem);
                    }
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        .container {
            width: 100%;
            padding: 0 .15rem;
        }

        .banner-section {
            height: 4rem;
        }

        .intro-section {
            padding: .6rem 0;

            .intro-content {
                h2 {
                    font-size: .24rem;
                }

                >p {
                    font-size: .14rem;
                }

                .intro-features {
                    flex-direction: column;
                    gap: .2rem;

                    .feature-item {
                        .feature-icon {
                            font-size: .3rem;
                        }

                        h3 {
                            font-size: .16rem;
                        }

                        p {
                            font-size: .12rem;
                        }
                    }
                }
            }
        }

        .timeline-section {
            padding: .6rem 0;

            .timeline-container {
                max-width: 100%;
                padding: 0 .2rem;

                // 移动端改为左侧连接线
                &::before {
                    left: .15rem;
                }

                .timeline-item {
                    flex-direction: row;
                    padding: 0;

                    .timeline-year {
                        flex: 0 0 .8rem;
                        text-align: left;
                        padding-right: .2rem;
                        padding-left: 0;
                        font-size: .14rem;
                    }

                    .timeline-content {
                        padding-left: .2rem;
                        padding-right: 0;
                        max-width: none;

                        .timeline-badge {
                            font-size: .1rem;
                            padding: .03rem .08rem;
                        }

                        .timeline-text {
                            font-size: .12rem;
                            padding: .12rem;

                            &::before {
                                right: -.06rem;
                                left: auto;
                                top: .15rem;
                                border-top: .06rem solid transparent;
                                border-bottom: .06rem solid transparent;
                                border-left: .06rem solid white;
                                border-right: none;
                            }

                            .timeline-year-display {
                                font-size: .14rem;
                                text-align: left;
                                margin-bottom: .06rem;
                            }

                            .timeline-image {
                                img {
                                    height: 1.2rem;
                                }
                            }
                        }
                    }

                    .timeline-badge {
                        left: .15rem;
                        top: .08rem;
                        font-size: .1rem;
                        padding: .03rem .08rem;
                    }

                    .timeline-dot {
                        display: none;
                    }

                    // 移动端统一左侧布局
                    &.timeline-item-right {
                        flex-direction: row;

                        .timeline-content {
                            padding-left: .2rem;
                            padding-right: 0;

                            .timeline-text {
                                &::before {
                                    right: -.06rem;
                                    left: auto;
                                    border-left: .06rem solid white;
                                    border-right: none;
                                }

                                .timeline-year-display {
                                    text-align: left;
                                }
                            }
                        }
                    }
                }
            }
        }

        .map-section,
        .partners-section {
            padding: .6rem 0;

            h2 {
                font-size: .24rem;
            }
        }

        .partners-grid {
            grid-template-columns: repeat(2, 1fr);
            max-width: 6rem;
            gap: .2rem;

            .partner-item {
                .partner-logo {
                    width: 1.2rem;
                    height: .9rem;
                    margin-bottom: .1rem;
                }

                .partner-name {
                    font-size: .12rem;
                }
            }
        }

        .intro-module {
            padding: .6rem 0;

            .intro-content {
                max-width: 100%;
                padding: 0 .2rem;

                h2 {
                    font-size: .22rem;
                    margin-bottom: .3rem;
                }

                p {
                    font-size: .14rem;
                    margin-bottom: .4rem;
                }

                .intro-tags {
                    gap: .1rem;

                    .intro-tag {
                        font-size: .12rem;
                        padding: .06rem .15rem;
                    }
                }
            }
        }
    }

    // 富文本模块
    .richtext-module {
        padding: .8rem 0;
        background: #f8f9fa;

        .richtext-content {
            .richtext-header {
                text-align: center;
                margin-bottom: .6rem;

                h2 {
                    font-size: .32rem;
                    color: #333;
                    font-weight: bold;
                    margin: 0;
                }
            }

            .richtext-intro {
                margin-bottom: .8rem;

                p {
                    font-size: .14rem;
                    line-height: 1.8;
                    color: #666;
                    margin-bottom: .4rem;
                    text-align: justify;
                    text-indent: .28rem;
                }
            }

            .richtext-cards {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: .3rem;

                .richtext-card {
                    background: white;
                    border-radius: .08rem;
                    overflow: hidden;
                    box-shadow: 0 .02rem .08rem rgba(0, 0, 0, 0.1);
                    border: 1px solid #e0e0e0;
                    transition: all 0.3s ease;

                    &:hover {
                        transform: translateY(-.02rem);
                        box-shadow: 0 .04rem .16rem rgba(0, 0, 0, 0.15);
                    }

                    .card-image {
                        width: 100%;
                        height: 3rem;
                        overflow: hidden;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            transition: transform 0.3s ease;
                        }

                        &:hover img {
                            transform: scale(1.05);
                        }
                    }

                    .card-content {
                        padding: .3rem;

                        h3 {
                            font-size: .16rem;
                            color: #333;
                            margin: 0 0 .2rem 0;
                            font-weight: bold;
                        }

                        p {
                            font-size: .12rem;
                            line-height: 1.6;
                            color: #666;
                            margin: .08rem 0;
                            text-align: justify;
                            text-indent: 0;
                        }
                    }
                }
            }
        }
    }

    // 富文本模块响应式设计
    @media (max-width: 768px) {
        .richtext-module {
            padding: .6rem 0;

            .richtext-content {
                .richtext-header {
                    margin-bottom: .4rem;

                    h2 {
                        font-size: .24rem;
                    }
                }

                .richtext-intro {
                    margin-bottom: .6rem;

                    p {
                        font-size: .12rem;
                        margin-bottom: .3rem;
                        text-indent: .24rem;
                    }
                }

                .richtext-cards {
                    grid-template-columns: 1fr;
                    gap: .2rem;

                    .richtext-card {
                        .card-image {
                            height: 2.5rem;
                        }

                        .card-content {
                            padding: .25rem;

                            h3 {
                                font-size: .14rem;
                                margin-bottom: .15rem;
                            }

                            p {
                                font-size: .11rem;
                                margin: .06rem 0;
                            }
                        }
                    }
                }
            }
        }
    }
}